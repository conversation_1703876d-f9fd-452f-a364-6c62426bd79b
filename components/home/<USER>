"use client";

import React, { useRef } from "react";
import { motion, AnimateNumber, useInView } from "motion/react";
import Wrapper from "@/components/Wrapper";
import Marquee from "react-fast-marquee";

const logos = [
  "/img1.jpg",
  "/img2.png",
  "/img3.webp",
  "/img4.webp",
  "/img5.webp",
  "/img6.webp",
  "/img7.webp",
  "/img8.jpg",
];

const AnimatedCounter = ({ target }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    amount: 0.3
  });

  return (
    <span ref={ref} className="font-bold text-blue-600">
      <AnimateNumber>{isInView ? target : 0}</AnimateNumber>
    </span>
  );
};

const TrustedBy = () => (
  <Wrapper padding="default" background="transparent">
    <section className="w-full flex flex-col items-center py-8 md:py-12">
      <motion.h4
        className="text-gray-600 font-bold mb-8 md:mb-12 text-center text-lg md:text-xl lg:text-2xl tracking-wider uppercase"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        TRUSTED BY MORE THAN <AnimatedCounter target={100} />+ COMPANIES WORLDWIDE
      </motion.h4>

      <motion.div
        className="w-full overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <Marquee
          gradient={false}
          speed={60}
          pauseOnHover={true}
          className="py-4"
        >
          {/* Duplicate logos for seamless continuous scrolling */}
          {[...logos, ...logos].map((logo, idx) => (
            <motion.div
              key={idx}
              className="mx-3 md:mx-4 flex items-center justify-center"
              whileHover={{
                scale: 1.1,
                transition: { duration: 0.2 }
              }}
            >
              <img
                src={logo}
                alt={`Trusted company logo ${(idx % logos.length) + 1}`}
                className="h-10 md:h-14 lg:h-16 object-contain"
              />
            </motion.div>
          ))}
        </Marquee>
      </motion.div>
    </section>
  </Wrapper>
);

export default TrustedBy;
